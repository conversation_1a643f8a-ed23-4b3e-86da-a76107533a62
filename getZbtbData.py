import datetime
import random
import hashlib
import time
import urllib
import mysqlcz
import requests

LF = "zxcvbnmlkjhgfdsaqwertyuiop0987654321QWERTYUIOPLKJHGFDSAZXCVBNM"
FNE = LF + "-@#$%^&*+!"


def qu(e):
    return ''.join(FNE[i] for i in e)


def dne(e, t=None):
    if t is None:
        return random.randint(1, e)
    else:
        return random.randint(e, t)


def nonce():
    return ''.join(LF[dne(0, 61)] for _ in range(16))


def timestamp():
    return int(time.time() * 1000)


def pne(e):
    if isinstance(e, dict):
        sorted_items = sorted(e.items())
        return '&'.join(f"{k}={v}" for k, v in sorted_items)
    elif isinstance(e, str):
        return '&'.join(sorted(e.split('&')))


def uk(str_):
    hash_object = hashlib.sha256()
    hash_object.update(str_.encode('utf-8'))
    return hash_object.hexdigest()


def signature(e):
    p = e.get('p', '')
    t = e.get('t', '')
    n = e.get('n', '')
    k = e.get('k', '')

    r = pne(p)
    return uk(n + k + urllib.parse.unquote(r) + t)


def test_signatures_algorithm():
    """
    测试 signatures 算法，使用你提供的示例数据
    """
    # 你提供的示例数据
    test_nonce = "eQCaleee7FvOC3vR"
    test_timestamp = "1753760449989"
    expected_signature = "86f696b1b22730560818b1d28d717daa69a9f09682375c717726dbf57d263a64"
    expected_signatures = "aaf3d17ad4fc4fad1438afb20d02223670551d0786d48a3aed5e5c9abb874a8e"

    # 我们需要推测参数p的值，基于你的代码逻辑
    # 假设是搜索参数
    test_params = 'dateType=&keyword=&openConvert=false&pageNo=1&pageSize=50&projectType=&publishEndTime=20250128235959&publishStartTime=20250128000000&searchType=pf&secondType=A&siteCode=44&thirdType=6&total=0&type=trading-type'

    # 测试现有的signature算法
    test_signature = signature({
        'p': test_params,
        't': test_timestamp,
        'n': test_nonce,
        'k': 'Bg5Wj$3UaYf'
    })

    print(f"测试signature算法:")
    print(f"期望值: {expected_signature}")
    print(f"计算值: {test_signature}")
    print(f"匹配: {test_signature == expected_signature}")
    print()

    # 测试signatures算法
    signatures_candidates = signatures({
        'p': test_params,
        't': test_timestamp,
        'n': test_nonce,
        'k': 'Bg5Wj$3UaYf',
        'app': 'ztbjg-portal'
    })

    print(f"测试signatures算法:")
    print(f"期望值: {expected_signatures}")
    for i, candidate in enumerate(signatures_candidates):
        print(f"候选值{i+1}: {candidate}")
        if candidate == expected_signatures:
            print(f"✓ 找到匹配的算法! 使用组合{i+1}")
            return i

    print("❌ 没有找到匹配的算法")
    return -1


def send(units):
    # 获取昨天和今天的日期
    # 获取今天的日期
    today = datetime.datetime.now().date()

    # 计算昨天的日期
    yesterdayNoFormat = today - datetime.timedelta(days=1)
    # 格式化昨天的日期
    yesterday = yesterdayNoFormat.strftime("%Y%m%d")
    yesterdayEndTime = yesterdayNoFormat.strftime("%Y%m%d") + "235959"
    yesterdayStartTime = yesterdayNoFormat.strftime("%Y%m%d") + "000000"

    # 打印昨天的日期
    params = 'dateType=&keyword=&openConvert=false&pageNo=1&pageSize=50&projectType=&publishEndTime=' + yesterdayEndTime + '&publishStartTime=' + yesterdayStartTime + '&searchType=pf&secondType=A&siteCode=44&thirdType=6&total=0&type=trading-type'
    print("params",params)
    headers = getHeader(params)
    print("headers", headers)
    pageNo = 1
    request_body = getRequestBody(pageNo, yesterdayEndTime, yesterdayStartTime)
    print("request_body",request_body)
    response = requests.post(
        'https://zbtb.gd.gov.cn/ztbjg-portal/center/notice/search',
        headers=headers,
        json=request_body
    )
    if response.status_code == 200:
        print("日期：",yesterday)
        # 首次数据直接处理
        filterData(response, units, yesterday)
        # 检查total是否大于现在的数据
        pageTotal = response.json().get('data', {}).get('pageTotal')
        print(pageTotal)
        i = 2
        while i <= pageTotal:
            params = 'dateType=&keyword=&openConvert=false&pageNo=' + str(
                i) + '&pageSize=50&projectType=&publishEndTime=' + yesterdayEndTime + '&publishStartTime=' + yesterdayStartTime + '&searchType=pf&secondType=A&siteCode=44&thirdType=6&total=0&type=trading-type'
            print(params)
            headers = getHeader(params)
            request_body = getRequestBody(i, yesterdayEndTime, yesterdayStartTime)
            response = requests.post(
                'https://zbtb.gd.gov.cn/ztbjg-portal/center/notice/search',
                headers=headers,
                json=request_body
            )
            filterData(response, units, yesterday)
            i += 1

    else:
        print('Error:', response.text)


def filterData(response, units, yesterday):
    # 将返回的数据插入到数据库中
    data = response.json().get('data', {}).get('pageData')
    for item in data:
        # 请求参数p
        p = 'projectCode=' + item["projectCode"] + '&tradingType=A&tradingProcess=' + item[
            "tradingProcess"] + '&siteCode=44'
        headers = getHeader(p)
        resp = requests.get(
            'https://zbtb.gd.gov.cn/ztbjg-portal/center/trading-notice/detail?' + p,
            headers=headers
        )
        operateUserName = resp.json().get('data', {})[0][0].get("operateUserName")
        print("获取到的招标单位",operateUserName)
        if operateUserName in units:
            price = resp.json().get('data', {})[0][0].get("controlledPrice")
            # 如果是符合传进来的单位，则写入到数据库中
            print("有符合数据，插入", operateUserName)
            mysqlcz.insert(str(item["noticeTitle"]), str(price), operateUserName, str(yesterday),
                           str("https://zbtb.gd.gov.cn/#/jygg/v2/" + item["projectCode"] + "/A/" + item[
                               "tradingProcess"]), "广东省招标投标监管网")


def getRequestBody(pageNo, yesterdayEndTime, yesterdayStartTime):
    request_body = {
        "dateType": "",
        "keyword": "",
        "openConvert": False,
        "pageNo": pageNo,
        "pageSize": 50,
        "projectType": "",
        "publishEndTime": yesterdayEndTime,
        "publishStartTime": yesterdayStartTime,
        "searchType": 'pf',
        "secondType": 'A',
        "siteCode": "44",
        "thirdType": 6,
        "total": 0,
        "type": "trading-type"
    }
    return request_body


def signatures(e):
    """
    尝试生成 x-dgi-req-signatures 的算法
    基于现有的签名算法进行推测
    """
    p = e.get('p', '')
    t = e.get('t', '')
    n = e.get('n', '')
    k = e.get('k', '')
    app = e.get('app', 'ztbjg-portal')

    # 尝试不同的组合方式
    # 方案1: 基于app + signature的组合
    original_signature = signature(e)

    # 方案2: 使用不同的参数顺序
    r = pne(p)

    # 尝试几种可能的组合
    combinations = [
        # 组合1: app + nonce + key + params + timestamp
        app + n + k + urllib.parse.unquote(r) + t,
        # 组合2: signature + app + timestamp
        original_signature + app + t,
        # 组合3: app + timestamp + nonce + key + params
        app + t + n + k + urllib.parse.unquote(r),
        # 组合4: nonce + app + key + params + timestamp
        n + app + k + urllib.parse.unquote(r) + t,
        # 组合5: key + app + nonce + params + timestamp
        k + app + n + urllib.parse.unquote(r) + t,
    ]

    return [uk(combo) for combo in combinations]


def getHeader(p):
    nonce_val = nonce()
    timestamp_val = timestamp()
    signature_val = signature({
        # 'p': 'dateType=&keyword=' + keyword + '&openConvert=false&pageNo=1&pageSize=50&projectType=' + projectType
        # + '&publishEndTime' '=' + yesterdayEndTime + '&publishStartTime=' + yesterdayStartTime +
        # '&searchType=pf&secondType=A&siteCode=44&thirdType=6&total=0&type=trading-type',
        'p': p,
        't': str(timestamp_val),  # 时间戳
        'n': nonce_val,  # 随机生成密钥,可固定
        'k': 'Bg5Wj$3UaYf'  # 常量
    })

    # 生成 signatures 候选值
    signatures_candidates = signatures({
        'p': p,
        't': str(timestamp_val),
        'n': nonce_val,
        'k': 'Bg5Wj$3UaYf',
        'app': 'ztbjg-portal'
    })

    headers = {
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
        "X-Dgi-Req-APP": "ztbjg-portal",
        "X-Dgi-Req-Nonce": nonce_val,
        "X-Dgi-Req-Signature": signature_val,
        "X-Dgi-Req-Signatures": signatures_candidates[0],  # 先尝试第一个候选值
        "X-Dgi-Req-Timestamp": str(timestamp_val),
    }
    return headers
