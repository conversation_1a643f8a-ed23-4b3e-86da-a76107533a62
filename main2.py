# 这是一个示例 Python 脚本。
import base64
import datetime
import os
import sys

import cv2
import numpy as np
# 按 Shift+F10 执行或将其替换为您的代码。
# 按 双击 Shift 在所有地方搜索类、文件、工具窗口、操作和设置。
import pytesseract
from PIL import Image
import requests
import urllib.request

import requests
from requests.exceptions import RequestException
import json
import hashlib
import random
import string
import mysqlcz

import schedule
import time





def get_code_img():
    code_img_url='https://gdgpo.czt.gd.gov.cn/freecms/verify/verifyCode.do?createTypeFlag=n&name=notice&d1709546643957'
    img_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.82 Safari/537.36',
        'Connection': 'close'
    }
    # 发送请求图片`
    res = requests.get(url=code_img_url, headers=img_headers)

    # 设置一个变量，该变量为指定保存的路径,windows系统下的 D盘，test目录
    dir_name = 'E:\\images'

    # 判断 指定目录，如果不存在该目录，则创建该目录
    if not os.path.exists(dir_name):
        os.mkdir(dir_name)
    # 指定路径（拼接上图片名及后缀
    path = dir_name + '\\verification.jpg'

    # 把获取的二进制写入图片文件  （注意：这里直接写入在Pycharm中可能只显示创建的目录而不显示文件，在文件夹中打开就可）
    with open(path, mode='wb') as f:
        f.write(res.content)
    # 将图片文件的保存路径返回
    return path



 #图片转二进制
def cv2_binary(image):
    img = cv2.imread(image)
    binary_str = cv2.imencode('.jpg', img)[1].tobytes()  # 编码
    print(binary_str)
    # base64_str = base64.b64encode(binary_str)#解码
    # base64_str = base64_str.decode('utf-8')
    # print(base64_str)
    return binary_str

#二进制转base64
def binary_base64(binary):
    img_stream = base64.b64encode(binary)
    bs64 = img_stream.decode('utf-8')
    return bs64

def GenerateHeader(api_auth_pubkey, app_auth_secretkey):
    api_auth_timestamp = str(int(time.time()))
    api_auth_nounce = "".join(random.sample(string.ascii_letters + string.digits, 10))
    HeaderDict = dict()
    prefix = "Value Exception"
    if api_auth_pubkey == "":
       raise Exception("the api_auth_pubkey must not be empty".format(prefix))
    if app_auth_secretkey == "":
       raise Exception("the app_auth_secret_key must not be empty".format(prefix))
    prefix = "Type Exception"
    if not isinstance(api_auth_pubkey, str):
       raise Exception("the type of api_auth_pubkey must be string".format(prefix))
    if not isinstance(app_auth_secretkey, str):
       raise Exception("the type of app_auth_secretkey must be string".format(prefix))
    HeaderDict["Api-Auth-nonce"] = api_auth_nounce
    HeaderDict["Api-Auth-pubkey"] = api_auth_pubkey
    HeaderDict["Api-Auth-timestamp"] = api_auth_timestamp
    token_name = hashlib.sha1()
    token_key = api_auth_nounce + api_auth_timestamp+app_auth_secretkey
    token_name.update(token_key.encode("utf-8"))
    HeaderDict["Api-Auth-sign"] = token_name.hexdigest()
    HeaderDict["Laiye-User-Region"] = "CN"
    HeaderDict["Content-Type"] = "application/json"
    return HeaderDict

#识别验证码
def verification():
    imagepath=get_code_img()
    binary=cv2_binary(imagepath)
    img_base64=binary_base64(binary)
    headers = GenerateHeader("iB9CpThQCnQkYzVx3uMHYsUX", "YF2ZPKir7UyNDa0t8WQhvXtnmmw59pwm")
    print(headers)
    url = "https://cloud.laiye.com/idp/v1/ocr/verification"

    payload = {
        "format": 0,
        "img_base64":img_base64}
    response = requests.post(url, headers=headers, json=payload)
    result = json.loads(response.text)
    return result["data"]["result"]
def getcgxx(purchaser,verification):
    current_time = datetime.datetime.now()
    print("当前时间为：", current_time.strftime("%Y-%m-%d %H:%M:%S"))

    # 计算当前时间早一天
    previous_day = current_time - datetime.timedelta(days=1)
    print("当前时间早一天为：", previous_day.strftime("%Y-%m-%d %H:%M:%S"))
    url = "https://gdgpo.czt.gd.gov.cn/freecms/rest/v1/notice/selectInfoMoreChannel.do?siteId=cd64e06a-21a7-4620-aebc-0576bab7e07a&channel=fca71be5-fc0c-45db-96af-f513e9abda9d&currPage=1&pageSize=10&noticeType=&regionCode=440001&verifyCode="+str(verification)+"&subChannel=false&purchaseManner=&title=&openTenderCode=&purchaser="+str(purchaser)+"&agency=&purchaseNature=&operationStartTime="+previous_day.strftime("%Y-%m-%d %H:%M:%S")+"&operationEndTime="+current_time.strftime("%Y-%m-%d %H:%M:%S")+"&selectTimeName=noticeTime&cityOrArea="
    payload = {}
    headers = {
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)'
    }
    response = requests.request("GET", url, headers=headers, data=payload)
    result = json.loads(response.text)
    print(type(result))  # <class 'dict'>
    buget=0.00
    for item in result["data"]:
          print(item["title"])  # Alice
          if(str(item["budget"])=='None'):
            buget = "请查看链接！"
          else:
            buget=item["budget"]
          print("https://gdgpo.czt.gd.gov.cn"+item["pageurl"])  # 25
          mysqlcz.insert(str(item["title"]), str(buget), str(purchaser), str(previous_day.strftime("%Y-%m-%d")),
                         str("https://gdgpo.czt.gd.gov.cn" + item["pageurl"]), "广东省政府采购网")

def stratcrawling():
    while True:
        runtime = 83000 # 开始时间
        nowtime = int(time.strftime('%H%M%S', time.localtime()))
        randomsecond = random.randint(5, 20)
        # 制作随机
        runtime = runtime + randomsecond
        # 隔一秒得到一次时间
        while True:
            if nowtime == runtime:
                try:
                    downloaddata()
                except Exception as e:
                    print(f"Find a error occurred:{e}")
                    time.sleep(random.randint(0, 60))
                    while Exception!=None:
                        try:
                            downloaddata()
                        except Exception as e:
                            print(f"Find a error occurred:{e}")
                            time.sleep(random.randint(0, 60))
                else:
                    break
            time.sleep(0.8)
            nowtime = int(time.strftime('%H%M%S', time.localtime()))
        break
# def stratcrawling():
#     while True:
#         runtime = 83000 # 开始时间
#         schedule.run_pending()
#         nowtime = int(time.strftime('%H%M%S', time.localtime()))
#         # 隔一秒得到一次时间
#         while True:
#             if nowtime == runtime:
#                 randomsecond = random.randint(0, 1800)
#                 # 制作随机
#                 runtime = runtime + randomsecond
#                 try:
#                     downloaddata()
#                 except Exception as e:
#                     print(f"Find a error occurred:{e}")
#                     time.sleep(random.randint(0, 60))
#                     while Exception!=None:
#                         try:
#                             downloaddata()
#                         except Exception as e:
#                             print(f"Find a error occurred:{e}")
#                             time.sleep(random.randint(0, 60))
#                 else:
#                     break
#             time.sleep(0.8)
#             nowtime = int(time.strftime('%H%M%S', time.localtime()))

def downloaddata():
    try:
        units=["广东省国土资源测绘院","广东省地图院","广东省国土资源技术中心","广东省土地调查规划院","广东省土地开发整治中心","广东省测绘产品质量监督检验中心","广东省国土资源档案馆","广东省海洋发展规划研究中心","广东省自然资源厅事务中心","广东省自然资源厅"]
        verifica = verification()
        for item in units:
            print(item)
            getcgxx(item, verifica)
    except Exception as e:
        print(f"Find a error occurred:{e}")



def messagesend():
    current_time = datetime.datetime.now()
    previous_day = current_time - datetime.timedelta(days=1)
    ID="wwb94dc095c2d38dbb"
    SECRET="zlIkXYHFQ5gLK2Mzj1NXwSUFxmUvKfKtPRIINbqjzc4"
    tokenurl="http://gdschjsyxgs.com.cn:8080/cgi-bin/gettoken?corpid=" +ID +"&corpsecret=" +SECRET
    print(tokenurl)
    tokeresponse = requests.request("GET", tokenurl)
    result = json.loads(tokeresponse.text)
    print(result)

    ascesstoken=result["access_token"]
    meurl = "http://gdschjsyxgs.com.cn:8080/cgi-bin/message/send?access_token="+ascesstoken
    payload = json.dumps({
        "touser": "@all",
        "msgtype": "text",
        "agentid": 1000007,
        "text": {
            "content": "项目采购清单已有新消息。\n打开链接可查看<a href=\"https://gdschjsyxgs.com.cn:5000/project/"+previous_day.strftime("%Y-%m-%d")+"\">"+previous_day.strftime("%Y-%m-%d")+"日采购项目信息</a>。"
        },
        "safe": 0,
        "enable_id_trans": 0,
        "enable_duplicate_check": 0,
        "duplicate_check_interval": 1800
    })
    print(payload)
    headers = {
        'Content-Type': 'application/json'
    }
    resultdata = mysqlcz.query(previous_day.strftime("%Y-%m-%d"));
    if(resultdata.__len__()>0):
        response = requests.request("POST", meurl, headers=headers, data=payload)


# # 定义任务计划，每天在指定时间段内随机执行
# def job():
#     start_hour, start_minute = 8, 30  # 开始时间：上午9:30
#     end_hour, end_minute = 8, 59  # 结束时间：下午5:00
#
#     # 获取当前时间
#     now = time.localtime()
#
#     # 判断当前时间是否在指定时间段内
#     if start_hour < now.tm_hour < end_hour or (start_hour == now.tm_hour and now.tm_min < start_minute) or (
#             end_hour == now.tm_hour and now.tm_min > end_minute):
#         # 如果在时间段内，等待一段随机时间后执行函数
#         wait_seconds = random.randint(60, 300)  # 随机等待时间为1分钟到1小时
#         time.sleep(wait_seconds)
#         downloaddata()


# schedule.every().day.at("15:43").do(stratcrawling)
# schedule.every().day.at("15:44").do(messagesend)
# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    # downloaddata()
   #
   # schedule.every().day.at("random").do(job)
    #schedule.every().hours.do(downloaddata)
    ##downloaddata()
    # while True:
    #     nowtime = int(time.strftime('%H%M%S', time.localtime()))
    #     if nowtime==80000:
    #         stratcrawling()
    #     elif nowtime==90500:
    #
    #     else:
    #         pass
    #     time.sleep(0.8)
    messagesend()


    # b = 80000
    # while True:
    #
    #     schedule.run_pending()
    #     a = time.strftime('%H%M%S', time.localtime())
    #     # 隔一秒得到一次时间
    #     if a == '80000':
    #         time.sleep(1)
    #
    #         # 如果时间为9点整
    #         b = int(a)
    #         # 把时间的变量类型改成int,赋值给b
    #         # o = random.randint(0, 59) * 100
    #         o=int(00)
    #         p = random.randint(0, 59)
    #         # 制作随机
    #         b = b + o + p
    #         # b增加随机数得出新的时间
    #         print(b)
    #         # 打印b,也可以不打印,就是方便看
    #     elif int(a) == b:
    #         downloaddata()







