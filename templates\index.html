<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购信息</title>
</head>
<style>
    .center-container {
        text-align: center; /* 文本居中对齐 */
    }

    .flex-container {
        display: flex;
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中 */
        height: 100vh; /* 容器高度设置为视口高度，用于垂直居中 */
    }

    /* 表格容器样式 */
    .table-container {
        width: 100%;
        overflow-x: auto; /* 允许水平滚动，如果表格太宽 */
    }

    /* 表格样式 */
    .my-table {
        width: 100%;
        border-collapse: collapse; /* 合并相邻的边框 */
        font-family: Arial, sans-serif; /* 字体样式 */
        font-size: 14px; /* 字体大小 */
        text-align: left; /* 文本对齐方式 */
    }

    /* 表格头部样式 */
    .my-table th {
        background-color: #f2f2f2; /* 背景颜色 */
        color: #333; /* 文本颜色 */
        padding: 10px; /* 内边距 */
        border: 1px solid #ddd; /* 边框样式 */
    }

    /* 表格行样式 */
    .my-table tr {
        border-bottom: 1px solid #ddd; /* 行间边框样式 */
    }

    /* 表格单元格样式 */
    .my-table td {
        padding: 10px; /* 内边距 */
        border: 1px solid #ddd; /* 边框样式 */
    }

    /* 表格行悬停样式 */
    .my-table tr:hover {
        background-color: #e5e5e5; /* 鼠标悬停时的背景颜色 */
    }
</style>
<body>
<div class="center-container">
    <h1>采购项目信息清单</h1>
    <p id="currentDate"></p>
</div>
<div class="table-container">
    <table class="my-table">
        <thead>
        <tr>
            <!-- 根据你的数据库表结构，列出表头 -->
            <th>项目序号</th>
            <th>项目名称</th>
            <th>采购金额(元)</th>
            <th>采购单位</th>
            <th>项目来源</th>
            <th>采购详情链接</th>
        </tr>
        </thead>
        <tbody>
        <!-- 使用Jinja2循环遍历查询结果，并为每行数据创建一个表格行 -->
        {% for row in rows %}
        <tr>
            <td>{{row[0]+1 }}</td>
            <td>{{row[1][1] }}</td>
            <td>{{row[1][2]}}</td>
            <td>{{row[1][4]}}</td>
            <td>{{row[1][6]}}</td>
            <td><a href="{{row[1][5]}}" target="_blank">详情</a></td>
            <!-- 根据数据库表的字段数量，添加更多单元格 -->
        </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
</body>
<script>
// 获取URL中的日期字符串
var url = window.location.href;
var queryDateStr = url.split("/").pop().replace(/-/g, ""); // 移除所有'-'字符
console.log(queryDateStr);

// 检查字符串长度是否符合预期
if (queryDateStr.length !== 8) {
    console.error("Invalid queryDate format: " + queryDateStr);
} else {
    // 解析日期字符串
    var year = parseInt(queryDateStr.substring(0, 4), 10);
    var month = parseInt(queryDateStr.substring(4, 6), 10) - 1; // JavaScript月份是从0开始的，所以需要减1
    var day = parseInt(queryDateStr.substring(6, 8), 10);

    // 创建Date对象
    let queryDate = new Date(year, month, day);

    if (isNaN(queryDate)) {
        console.error("Invalid queryDate format: " + queryDateStr);
    } else {
        // 计算前一天的日期
        let previousDay = new Date(queryDate);
        // previousDay.setDate(previousDay.getDate() - 1);

        // 设置前一天上午8:30
        let beginDate = new Date(previousDay);
        beginDate.setHours(8, 30, 0, 0); // 设置时间为前一天上午8:30

        // 设置当天上午8:30
        let finishDate = new Date(queryDate);
        finishDate.setDate(finishDate.getDate()+1)
        finishDate.setHours(8, 30, 0, 0); // 设置时间为当天上午8:30

        // 格式化日期
        let formattedBeginDate = beginDate.toLocaleString('default', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        }).replace(/\//g, '-');

        let formattedFinishDate = finishDate.toLocaleString('default', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        }).replace(/\//g, '-');

        var dateElement = document.getElementById('currentDate');

        // 将格式化后的日期设置为元素的文本内容
        if (dateElement) {
            dateElement.textContent = "统计时间为：" + formattedBeginDate + " 至 " + formattedFinishDate;
        }
    }
}
</script>
</html>