import pymysql




def  connectmysql():
    # 建立与MySQL服务器的连接
    conn = pymysql.connect(host='***************', port=3306, user='root', password='jsgs@2023', database='cgxx')
    return  conn


def query(date):
# 执行查询语句
    conn=connectmysql()
    cursor=conn.cursor()
    sql = "SELECT * FROM projectinfo where projectdate=\'"+date+"\'"
    print(sql)
    cursor.execute(sql)
    rows = cursor.fetchall()
    indexed_rows = [[index, row] for index, row in enumerate(rows)]
    # 关闭连接
    cursor.close()
    conn.close()
    return indexed_rows

def insert(name,buget,cgdw,date,pageurl,source):
    conn = connectmysql()
    cursor = conn.cursor()
    sql = "INSERT INTO cgxx.projectinfo (projectname, price, purchasingunit,projectdate, url,source) VALUES (\'"+name+"\',\'"+ buget+"\',\'"+cgdw+"\',\'"+date+"\',\'"+pageurl+"\',\'"+source+"\')"
    print(sql)
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        print("Error:", str(e))
    finally:
        cursor.close()
        conn.close()

