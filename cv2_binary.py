import cv2
import base64


def cv2_binary(image):
    img = cv2.imread(image)
    binary_str = cv2.imencode('.jpg', img)[1].tobytes()  # 编码
    print(binary_str)
    # base64_str = base64.b64encode(binary_str)#解码
    # base64_str = base64_str.decode('utf-8')
    # print(base64_str)
    return binary_str

def binary_base64(binary):
    img_stream = base64.b64encode(binary)
    bs64 = img_stream.decode('utf-8')
    print(bs64)


if __name__ == '__main__':
    cv2_binary("verifyCode.jpg")
    # 或者
    image_file = r"verifyCode.jpg"
    image_bytes = open(image_file, "rb").read()
    binary_base64(image_bytes)  # 二进制数据